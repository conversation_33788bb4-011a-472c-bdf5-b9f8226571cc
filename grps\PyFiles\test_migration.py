#!/usr/bin/env python3
"""
Test script to verify the migration from raw Selenium to Enhanced SeleniumBase Driver
This script tests the basic functionality of the migrated groups.py
"""

import sys
import os
import logging

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_enhanced_driver_import():
    """Test that enhanced driver components can be imported"""
    try:
        from updated_groups import EnhancedSeleniumBaseDriver, ProfileManager, SessionManager, BehavioralSimulator
        print("✅ Enhanced driver components imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import enhanced driver components: {e}")
        return False

def test_groups_import():
    """Test that groups.py can be imported with enhanced driver"""
    try:
        # This will test the import and initialization logic
        import groups
        print("✅ groups.py imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import groups.py: {e}")
        return False
    except Exception as e:
        print(f"⚠️ groups.py imported but with warnings: {e}")
        return True

def test_driver_initialization():
    """Test that the Driver class can be initialized"""
    try:
        from groups import Driver
        
        # Test parameters
        test_email = "<EMAIL>"
        test_password = "testpass123"
        test_ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        test_index = 1
        
        print("🧪 Testing Driver initialization...")
        
        # This should work if enhanced driver is available
        driver = Driver(test_email, test_password, test_ua, test_index)
        
        # Test that browser is accessible
        if hasattr(driver, 'browser'):
            print("✅ Driver initialized successfully with browser instance")
            
            # Test that enhanced driver methods are accessible
            if hasattr(driver, '_enhanced_driver'):
                print("✅ Enhanced driver instance accessible")
                
                # Test profile management
                if hasattr(driver._enhanced_driver, 'profile_config'):
                    print("✅ Profile management available")
                    
                # Test session management
                if hasattr(driver._enhanced_driver, 'session_manager'):
                    print("✅ Session management available")
                    
                # Test behavioral simulation
                if hasattr(driver._enhanced_driver, 'behavioral_simulator'):
                    print("✅ Behavioral simulation available")
            
            # Clean up
            try:
                driver.finish()
                print("✅ Driver cleanup successful")
            except:
                print("⚠️ Driver cleanup had issues (expected in test environment)")
                
            return True
        else:
            print("❌ Driver initialized but browser not accessible")
            return False
            
    except ImportError as e:
        print(f"❌ Enhanced driver not available for testing: {e}")
        return False
    except Exception as e:
        print(f"❌ Driver initialization failed: {e}")
        return False

def test_worker_class():
    """Test that Worker class can be initialized"""
    try:
        from groups import Worker
        
        # Test Worker initialization
        test_actions = ["test_action"]
        worker = Worker(test_actions)
        
        if hasattr(worker, 'logger'):
            print("✅ Worker class initialized successfully")
            return True
        else:
            print("❌ Worker class missing expected attributes")
            return False
            
    except Exception as e:
        print(f"❌ Worker class initialization failed: {e}")
        return False

def test_enhanced_methods():
    """Test that enhanced methods are available through delegation"""
    try:
        from groups import Driver
        
        test_email = "<EMAIL>"
        test_password = "testpass123"
        test_ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        test_index = 1
        
        driver = Driver(test_email, test_password, test_ua, test_index)
        
        # Test enhanced driver method delegation
        enhanced_methods = [
            'go', 'find_xpath', 'find_css', 'execute_js', 
            'wait_xpath_presence', 'wait_css_clickable',
            'human_click_element', 'human_type_text', 'human_scroll_page'
        ]
        
        available_methods = []
        for method in enhanced_methods:
            if hasattr(driver, method):
                available_methods.append(method)
        
        print(f"✅ Enhanced methods available: {len(available_methods)}/{len(enhanced_methods)}")
        print(f"   Available: {', '.join(available_methods)}")
        
        # Clean up
        try:
            driver.finish()
        except:
            pass
            
        return len(available_methods) > 0
        
    except Exception as e:
        print(f"❌ Enhanced methods test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Enhanced SeleniumBase Driver Migration")
    print("=" * 60)
    
    tests = [
        ("Enhanced Driver Import", test_enhanced_driver_import),
        ("Groups.py Import", test_groups_import),
        ("Driver Initialization", test_driver_initialization),
        ("Worker Class", test_worker_class),
        ("Enhanced Methods", test_enhanced_methods)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Migration appears successful.")
    elif passed > 0:
        print("⚠️ Some tests passed. Migration partially successful.")
    else:
        print("❌ All tests failed. Migration needs attention.")
    
    return passed == len(results)

if __name__ == "__main__":
    main()
