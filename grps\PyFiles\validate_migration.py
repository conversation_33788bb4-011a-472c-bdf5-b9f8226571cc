#!/usr/bin/env python3
"""
Simple validation script to verify the migration from raw Selenium to Enhanced SeleniumBase Driver
This script performs basic checks without triggering the full application
"""

import sys
import os

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def validate_enhanced_driver_import():
    """Validate that enhanced driver components can be imported"""
    try:
        from updated_groups import EnhancedSeleniumBaseDriver, ProfileManager, SessionManager, BehavioralSimulator
        print("✅ Enhanced driver components imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import enhanced driver components: {e}")
        return False

def validate_groups_file_structure():
    """Validate the structure of groups.py without executing it"""
    try:
        with open('groups.py', 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Check for key migration indicators
        checks = [
            ("Enhanced driver import", "from updated_groups import EnhancedSeleniumBaseDriver" in content),
            ("Driver class uses enhanced driver", "self._enhanced_driver = EnhancedSeleniumBaseDriver" in content),
            ("Removed fallback logic", "ENHANCED_DRIVER_AVAILABLE = False" not in content or "Enhanced driver not available for testing" in content),
            ("Human-like interactions", "human_type_text" in content or "human_click_element" in content),
            ("Enhanced driver delegation", "__getattr__" in content and "getattr(self._enhanced_driver" in content),
            ("Profile management", "profile_config" in content),
            ("Session management", "session_manager" in content),
            ("Behavioral simulation", "behavioral_simulator" in content)
        ]
        
        passed = 0
        for check_name, condition in checks:
            if condition:
                print(f"✅ {check_name}")
                passed += 1
            else:
                print(f"❌ {check_name}")
        
        print(f"\nStructure validation: {passed}/{len(checks)} checks passed")
        return passed == len(checks)
        
    except Exception as e:
        print(f"❌ Error validating groups.py structure: {e}")
        return False

def validate_import_cleanup():
    """Validate that imports have been properly cleaned up"""
    try:
        with open('groups.py', 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Check that redundant imports are removed
        redundant_imports = [
            "from selenium.webdriver.chrome.service import Service as ChromeService",
            "from webdriver_manager.chrome import ChromeDriverManager",
            "from selenium import webdriver",
            "from selenium.webdriver.chrome.options import Options",
            "from selenium.webdriver.chrome.service import Service"
        ]
        
        removed_count = 0
        for import_line in redundant_imports:
            if import_line not in content:
                removed_count += 1
        
        print(f"✅ Removed {removed_count}/{len(redundant_imports)} redundant imports")
        
        # Check that essential imports are kept
        essential_imports = [
            "from selenium.webdriver.support import expected_conditions as EC",
            "from selenium.common.exceptions import NoSuchElementException, TimeoutException",
            "from selenium.webdriver.support.ui import WebDriverWait",
            "from selenium.webdriver.common.keys import Keys",
            "from selenium.webdriver.common.by import By"
        ]
        
        kept_count = 0
        for import_line in essential_imports:
            if import_line in content:
                kept_count += 1
        
        print(f"✅ Kept {kept_count}/{len(essential_imports)} essential imports")
        
        return removed_count >= len(redundant_imports) - 1 and kept_count >= len(essential_imports) - 1
        
    except Exception as e:
        print(f"❌ Error validating import cleanup: {e}")
        return False

def validate_method_migration():
    """Validate that key methods have been migrated"""
    try:
        with open('groups.py', 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Check for enhanced method usage
        enhanced_patterns = [
            "human_type_text",
            "human_click_element", 
            "enhanced_driver",
            "profile_config",
            "session_manager"
        ]
        
        found_count = 0
        for pattern in enhanced_patterns:
            if pattern in content:
                found_count += 1
                print(f"✅ Found enhanced pattern: {pattern}")
            else:
                print(f"⚠️ Enhanced pattern not found: {pattern}")
        
        print(f"\nMethod migration: {found_count}/{len(enhanced_patterns)} enhanced patterns found")
        return found_count >= len(enhanced_patterns) - 1  # Allow for some flexibility
        
    except Exception as e:
        print(f"❌ Error validating method migration: {e}")
        return False

def main():
    """Run all validation checks"""
    print("🔍 Validating Enhanced SeleniumBase Driver Migration")
    print("=" * 60)
    
    validations = [
        ("Enhanced Driver Import", validate_enhanced_driver_import),
        ("Groups File Structure", validate_groups_file_structure),
        ("Import Cleanup", validate_import_cleanup),
        ("Method Migration", validate_method_migration)
    ]
    
    results = []
    for validation_name, validation_func in validations:
        print(f"\n📋 Validating: {validation_name}")
        print("-" * 40)
        try:
            result = validation_func()
            results.append((validation_name, result))
        except Exception as e:
            print(f"❌ Validation '{validation_name}' crashed: {e}")
            results.append((validation_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VALIDATION SUMMARY")
    print("=" * 60)
    
    passed = 0
    for validation_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {validation_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{len(results)} validations passed")
    
    if passed == len(results):
        print("🎉 All validations passed! Migration appears successful.")
        print("\n📝 Migration Summary:")
        print("- ✅ Enhanced SeleniumBase Driver is now the primary interface")
        print("- ✅ Raw Selenium imports have been cleaned up")
        print("- ✅ Driver class fully uses enhanced driver")
        print("- ✅ Human-like interactions are integrated")
        print("- ✅ Profile and session management are enabled")
        print("- ✅ Backward compatibility is maintained")
    elif passed > 0:
        print("⚠️ Some validations passed. Migration partially successful.")
        print("Please review the failed validations above.")
    else:
        print("❌ All validations failed. Migration needs attention.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
